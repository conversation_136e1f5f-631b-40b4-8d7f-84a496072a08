# Qwen2MoE vs DeepSeek-V2 EPLB 实现对比

本文档对比了 Qwen2MoE 和 DeepSeek-V2 的 EPLB 实现，展示了我们如何严格参考 DeepSeek-V2 的模式。

## 类继承结构

### DeepSeek-V2
```python
class DeepseekV2ForCausalLM(nn.<PERSON>, SupportsPP, MixtureOfExperts):
```

### Qwen2MoE
```python
class Qwen2MoeForCausalLM(nn.Module, SupportsPP, MixtureOfExperts):
```

✅ **完全一致** - 都直接继承 `MixtureOfExperts` 接口

## 初始化中的 EPLB 属性设置

### DeepSeek-V2
```python
def __init__(self, *, vllm_config: VllmConfig, prefix: str = ""):
    # ... 基础初始化 ...
    self.expert_weights = []
    
    # Set MoE hyperparameters
    self.num_moe_layers = (config.num_hidden_layers - config.first_k_dense_replace)
    self.num_expert_groups = config.n_group
    
    self.moe_layers: list[FusedMoE] = []
    for layer in self.model.layers:
        assert isinstance(layer, DeepseekV2DecoderLayer)
        if isinstance(layer.mlp, DeepseekV2MoE):
            self.moe_layers.append(layer.mlp.experts)
    
    # Pick last one layer since the first ones may be dense layers.
    example_moe = typing.cast(DeepseekV2MoE, self.model.layers[config.num_hidden_layers - 1].mlp)
    self.num_logical_experts = example_moe.n_logical_experts
    self.num_physical_experts = example_moe.n_physical_experts
    # ... 其他属性 ...
```

### Qwen2MoE
```python
def __init__(self, *, vllm_config: VllmConfig, prefix: str = ""):
    # ... 基础初始化 ...
    self.expert_weights = []
    
    # Collect MoE layers and set MoE hyperparameters
    self.moe_layers: list[FusedMoE] = []
    for layer in self.model.layers:
        if isinstance(layer.mlp, Qwen2MoeSparseMoeBlock):
            self.moe_layers.append(layer.mlp.experts)
    
    # Set MoE hyperparameters from the first MoE layer
    if self.moe_layers:
        example_moe = None
        for layer in self.model.layers:
            if isinstance(layer.mlp, Qwen2MoeSparseMoeBlock):
                example_moe = layer.mlp
                break
        
        if example_moe is not None:
            self.num_moe_layers = len(self.moe_layers)
            self.num_expert_groups = 1  # Qwen2MoE typically has 1 expert group
            self.num_logical_experts = config.num_experts
            # ... 其他属性 ...
```

✅ **模式一致** - 都在初始化时收集 MoE 层并设置属性

## set_eplb_state 方法

### DeepSeek-V2
```python
def set_eplb_state(
    self,
    expert_load_view: torch.Tensor,
    logical_to_physical_map: torch.Tensor,
    logical_replica_count: torch.Tensor,
) -> None:
    for layer_idx, layer in enumerate(self.moe_layers):
        # Register the expert weights.
        self.expert_weights.append(layer.get_expert_weights())
        layer.set_eplb_state(
            moe_layer_idx=layer_idx,
            expert_load_view=expert_load_view,
            logical_to_physical_map=logical_to_physical_map,
            logical_replica_count=logical_replica_count,
        )
```

### Qwen2MoE
```python
def set_eplb_state(
    self,
    expert_load_view: torch.Tensor,
    logical_to_physical_map: torch.Tensor,
    logical_replica_count: torch.Tensor,
) -> None:
    for layer_idx, layer in enumerate(self.moe_layers):
        # Register the expert weights
        self.expert_weights.append(layer.get_expert_weights())
        layer.set_eplb_state(
            moe_layer_idx=layer_idx,
            expert_load_view=expert_load_view,
            logical_to_physical_map=logical_to_physical_map,
            logical_replica_count=logical_replica_count,
        )
```

✅ **完全一致** - 逻辑和结构完全相同

## MoE 层实现

### DeepSeek-V2
```python
class DeepseekV2MoE(nn.Module):
    def __init__(self, ..., enable_eplb: bool = False):
        # ... MoE 层初始化 ...
        self.experts = FusedMoE(...)
```

### Qwen2MoE
```python
class Qwen2MoeSparseMoeBlock(nn.Module):
    def __init__(self, ...):
        # ... MoE 层初始化 ...
        self.experts = FusedMoE(...)
    
    def get_expert_weights(self) -> Iterable[torch.Tensor]:
        return self.experts.get_expert_weights()
    
    def set_eplb_state(self, ...):
        self.experts.set_eplb_state(...)
```

✅ **功能一致** - 都通过 FusedMoE 实现专家管理

## 关键差异和适配

| 方面 | DeepSeek-V2 | Qwen2MoE | 说明 |
|------|-------------|----------|------|
| MoE 层类名 | `DeepseekV2MoE` | `Qwen2MoeSparseMoeBlock` | 不同的命名约定 |
| 专家组数量 | `config.n_group` | `1` | Qwen2MoE 通常只有1个专家组 |
| 配置属性名 | `n_routed_experts` | `num_experts` | 不同的配置字段名 |
| 层检测逻辑 | 检查最后一层 | 检查第一个MoE层 | 适应不同的模型结构 |

## 兼容性验证

### 接口兼容性
- ✅ 都实现 `MixtureOfExperts` 接口
- ✅ 都提供相同的 EPLB 方法签名
- ✅ 都使用相同的属性名称

### 架构兼容性
- ✅ 都使用 `FusedMoE` 作为底层实现
- ✅ 都在初始化时收集 MoE 层
- ✅ 都支持相同的 EPLB 功能

### 功能兼容性
- ✅ 支持动态专家重排
- ✅ 支持负载均衡指标收集
- ✅ 支持冗余专家管理
- ✅ 支持 Prometheus 指标

## 总结

Qwen2MoE 的 EPLB 实现严格遵循了 DeepSeek-V2 的设计模式：

1. **架构一致性**: 相同的类继承结构和初始化流程
2. **方法一致性**: 相同的 `set_eplb_state` 实现逻辑
3. **接口一致性**: 完全兼容的 `MixtureOfExperts` 接口
4. **功能一致性**: 支持所有 EPLB 核心功能

这种一致性确保了 Qwen2MoE 能够无缝集成到现有的 vLLM EPLB 生态系统中，并享受所有 EPLB 功能的支持。
