#!/usr/bin/env python3
"""
Structure check for Qwen2MoE EPLB implementation.

This script checks the code structure without importing torch dependencies.
"""

import ast
import sys

def check_class_methods(file_path):
    """Check if required methods are present in the classes."""
    print("Checking class structure...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    try:
        tree = ast.parse(content)
    except SyntaxError as e:
        print(f"✗ Syntax error: {e}")
        return False
    
    # Find classes and their methods
    classes = {}
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            methods = []
            properties = []
            for item in node.body:
                if isinstance(item, ast.FunctionDef):
                    methods.append(item.name)
                elif isinstance(item, ast.FunctionDef) and any(
                    isinstance(decorator, ast.Name) and decorator.id == 'property'
                    for decorator in item.decorator_list
                ):
                    properties.append(item.name)
                elif isinstance(item, ast.FunctionDef):
                    # Check for @property decorator
                    for decorator in item.decorator_list:
                        if (isinstance(decorator, ast.Name) and decorator.id == 'property') or \
                           (isinstance(decorator, ast.Attribute) and decorator.attr == 'property'):
                            properties.append(item.name)
                            break
                    else:
                        methods.append(item.name)
            
            classes[node.name] = {
                'methods': methods,
                'properties': properties
            }
    
    # Check Qwen2MoeForCausalLM
    if 'Qwen2MoeForCausalLM' in classes:
        causal_lm = classes['Qwen2MoeForCausalLM']
        required_methods = ['set_eplb_state', '_collect_moe_layers']
        required_properties = [
            'num_moe_layers', 'num_expert_groups', 'num_logical_experts',
            'num_physical_experts', 'num_local_physical_experts', 
            'num_routed_experts', 'num_redundant_experts'
        ]
        
        print("Checking Qwen2MoeForCausalLM...")
        for method in required_methods:
            if method in causal_lm['methods']:
                print(f"  ✓ Method {method} found")
            else:
                print(f"  ✗ Method {method} missing")
                return False
        
        for prop in required_properties:
            if prop in causal_lm['properties'] or prop in causal_lm['methods']:
                print(f"  ✓ Property {prop} found")
            else:
                print(f"  ✗ Property {prop} missing")
                return False
    else:
        print("✗ Qwen2MoeForCausalLM class not found")
        return False
    
    # Check Qwen2MoeSparseMoeBlock
    if 'Qwen2MoeSparseMoeBlock' in classes:
        moe_block = classes['Qwen2MoeSparseMoeBlock']
        required_methods = ['get_expert_weights', 'set_eplb_state']
        
        print("Checking Qwen2MoeSparseMoeBlock...")
        for method in required_methods:
            if method in moe_block['methods']:
                print(f"  ✓ Method {method} found")
            else:
                print(f"  ✗ Method {method} missing")
                return False
    else:
        print("✗ Qwen2MoeSparseMoeBlock class not found")
        return False
    
    return True

def check_imports(file_path):
    """Check if required imports are present."""
    print("Checking imports...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    required_imports = [
        'MixtureOfExperts',
        'MutableSequence',
        'Tensor'
    ]
    
    for imp in required_imports:
        if imp in content:
            print(f"  ✓ {imp} imported")
        else:
            print(f"  ✗ {imp} not found in imports")
            return False
    
    return True

def main():
    """Run structure checks."""
    file_path = 'vllm/model_executor/models/qwen2_moe.py'
    
    print("Running Qwen2MoE structure checks...\n")
    
    checks = [
        lambda: check_imports(file_path),
        lambda: check_class_methods(file_path),
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        if check():
            passed += 1
        print()
    
    print(f"Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All structure checks passed! EPLB implementation structure looks good.")
        return 0
    else:
        print("❌ Some checks failed. Please review the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
