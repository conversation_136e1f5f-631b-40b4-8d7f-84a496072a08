# EPLB Metrics 监控指南

本指南介绍如何使用 vLLM 的 EPLB (Expert Parallelism Load Balancing) 指标来监控和优化专家并行负载均衡的质量。

## 概述

EPLB 指标系统提供了全面的监控能力，帮助您：
- 评估负载均衡质量
- 识别性能瓶颈
- 优化配置参数
- 监控系统健康状态

## 启用 EPLB 指标

### 1. 基本配置

```bash
# 使用新的 ep_config 格式
--ep-config='{"enable_eplb": true, "lb_window_size": 1000, "lb_step_size": 3000, "lb_log_balancedness": true, "lb_collect_metrics": true}'

# 或使用传统格式
--enable-eplb --eplb-window-size 1000 --eplb-step-interval 3000 --eplb-log-balancedness --enable-metrics
```

### 2. Python API 配置

```python
from vllm import LLM
from vllm.config import EPConfig

ep_config = EPConfig(
    enable_eplb=True,
    lb_window_size=1000,
    lb_step_size=3000,
    lb_log_balancedness=True,
    lb_collect_metrics=True
)

llm = LLM(
    model="your-moe-model",
    enable_expert_parallel=True,
    ep_config=ep_config,
    enable_metrics=True,
    metrics_port=8080
)
```

## 核心指标说明

### 1. 负载均衡质量指标

#### `vllm_eplb_balancedness`
- **含义**: 负载均衡比率 (平均负载 / 最大负载)
- **范围**: [0, 1]，1 表示完美均衡
- **目标**: > 0.8 为良好，> 0.9 为优秀
- **告警阈值**: < 0.7

#### `vllm_eplb_load_variance`
- **含义**: 各 rank 间负载分布的方差
- **目标**: 越小越好
- **用途**: 识别负载分布不均

#### `vllm_eplb_expert_utilization_gini`
- **含义**: 专家利用率的基尼系数
- **范围**: [0, 1]，0 表示完全平等，1 表示最大不平等
- **目标**: < 0.3 为良好

### 2. 负载分布指标

#### `vllm_eplb_avg_tokens_per_rank`
- **含义**: 每个 rank 处理的平均 token 数
- **用途**: 监控整体负载水平

#### `vllm_eplb_max_tokens_per_rank`
- **含义**: 最繁忙 rank 处理的 token 数
- **用途**: 识别负载热点

### 3. 专家复制指标

#### `vllm_eplb_avg_expert_replicas`
- **含义**: 每个逻辑专家的平均副本数
- **用途**: 监控冗余专家的分布

### 4. 重排性能指标

#### `vllm_eplb_rearrangements_total`
- **含义**: 专家重排的总次数
- **用途**: 监控重排频率

#### `vllm_eplb_rearrangement_time_seconds`
- **含义**: 专家重排耗时分布
- **用途**: 监控重排性能开销

#### `vllm_eplb_steps_since_rearrangement`
- **含义**: 距离上次重排的步数
- **用途**: 监控重排时机

## 监控最佳实践

### 1. 关键指标监控

```promql
# 负载均衡质量
vllm_eplb_balancedness < 0.7

# 重排频率异常
rate(vllm_eplb_rearrangements_total[5m]) > 0.1

# 重排耗时过长
histogram_quantile(0.95, rate(vllm_eplb_rearrangement_time_seconds_bucket[5m])) > 1.0
```

### 2. 告警规则示例

```yaml
groups:
- name: eplb_alerts
  rules:
  - alert: EPLBLowBalancedness
    expr: vllm_eplb_balancedness < 0.7
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "EPLB load balancedness is low"
      description: "Balancedness ratio is {{ $value }}, indicating poor load distribution"

  - alert: EPLBHighRearrangementTime
    expr: histogram_quantile(0.95, rate(vllm_eplb_rearrangement_time_seconds_bucket[5m])) > 2.0
    for: 1m
    labels:
      severity: warning
    annotations:
      summary: "EPLB rearrangement taking too long"
      description: "95th percentile rearrangement time is {{ $value }}s"
```

### 3. 性能优化建议

#### 基于指标的参数调优

```python
# 如果 balancedness < 0.8，考虑：
ep_config.lb_step_size = 2000  # 减少重排间隔

# 如果 rearrangement_time > 1s，考虑：
ep_config.lb_step_size = 5000  # 增加重排间隔

# 如果 expert_utilization_gini > 0.4，考虑：
# 增加冗余专家数量
num_redundant_experts = 64
```

## Grafana 仪表板

使用提供的 `grafana_eplb_dashboard.json` 创建监控仪表板：

1. 导入仪表板配置
2. 配置 Prometheus 数据源
3. 设置适当的刷新间隔（建议 5-10 秒）

### 关键面板说明

- **Load Balancedness**: 实时负载均衡质量
- **Token Distribution**: 各 rank 的 token 分布
- **Rearrangement Metrics**: 重排频率和耗时
- **Expert Utilization**: 专家使用分布

## 故障排查

### 常见问题

1. **均衡性持续较低**
   - 检查模型的专家路由模式
   - 调整 `lb_step_size` 参数
   - 增加冗余专家数量

2. **重排耗时过长**
   - 检查网络带宽
   - 优化通信拓扑
   - 考虑增加 `lb_step_size`

3. **指标未更新**
   - 确认 `lb_collect_metrics=true`
   - 检查 Prometheus 配置
   - 验证网络连接

### 调试命令

```bash
# 检查指标端点
curl http://localhost:8080/metrics | grep vllm_eplb

# 查看详细日志
tail -f vllm.log | grep EPLB
```

## 性能影响

启用 EPLB 指标的性能影响：
- CPU 开销: < 1%
- 内存开销: < 10MB
- 网络开销: 最小（仅在重排时）

建议在生产环境中启用，以获得完整的监控能力。
