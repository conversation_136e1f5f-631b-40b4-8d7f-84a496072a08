# Qwen2MoE EPLB Implementation

本文档描述了为 Qwen2MoE 模型添加 EPLB (Expert Parallelism Load Balancing) 支持的实现细节。

## 概述

EPLB 是一种专家并行负载均衡技术，通过动态重排专家权重来优化 MoE (Mixture of Experts) 模型的负载分布。本实现为 Qwen2MoE 模型添加了完整的 EPLB 支持。

## 实现的主要组件

### 1. MixtureOfExperts 接口实现

`Qwen2MoeForCausalLM` 类现在实现了 `MixtureOfExperts` 接口，提供以下属性和方法：

#### 属性
- `expert_weights`: 专家权重的可变序列
- `num_moe_layers`: MoE 层数量
- `num_expert_groups`: 专家组数量
- `num_logical_experts`: 逻辑专家数量
- `num_physical_experts`: 物理专家数量（包括冗余专家）
- `num_local_physical_experts`: 本地物理专家数量
- `num_routed_experts`: 路由专家数量
- `num_redundant_experts`: 冗余专家数量

#### 方法
- `set_eplb_state()`: 设置 EPLB 状态
- `_collect_moe_layers()`: 收集 MoE 层

### 2. MoE 层 EPLB 支持

`Qwen2MoeSparseMoeBlock` 类添加了 EPLB 支持方法：

- `get_expert_weights()`: 获取专家权重
- `set_eplb_state()`: 设置层级 EPLB 状态

### 3. 专家权重管理

实现自动收集和管理专家权重，支持：
- 动态专家权重收集
- EPLB 状态传播
- 负载均衡指标记录

## 使用方法

### 基本配置

```python
from vllm import LLM
from vllm.config import EPConfig

# 配置 EPLB
ep_config = EPConfig(
    enable_eplb=True,
    lb_window_size=1000,
    lb_step_size=3000,
    lb_log_balancedness=True,
    lb_collect_metrics=True
)

# 初始化 LLM
llm = LLM(
    model="Qwen/Qwen2.5-MoE-A14B-Chat",
    enable_expert_parallel=True,
    ep_config=ep_config,
    num_redundant_experts=32
)
```

### 命令行配置

```bash
# 使用新的 ep_config 格式
--ep-config='{"enable_eplb": true, "lb_window_size": 1000, "lb_step_size": 3000, "lb_collect_metrics": true}'

# 或使用传统格式
--enable-eplb --eplb-window-size 1000 --eplb-step-interval 3000 --eplb-log-balancedness
```

## 技术细节

### 专家并行处理

- 支持动态专家数量计算
- 自动处理冗余专家
- 兼容现有的张量并行

### 负载均衡

- 实时负载监控
- 动态专家重排
- 负载均衡指标收集

### 指标收集

- 负载均衡度指标
- 专家利用率统计
- 重排频率监控
- Prometheus 指标支持

## 兼容性

### 支持的模型

- Qwen2.5-MoE-A14B-Chat
- Qwen2-MoE-57B-A14B
- 其他 Qwen2MoE 系列模型

### 并行策略

- 专家并行 (EP)
- 张量并行 (TP)
- 数据并行 (DP)
- 混合并行策略

## 性能优化

### 配置建议

1. **窗口大小 (lb_window_size)**
   - 较小值：更快响应负载变化
   - 较大值：更稳定的负载统计

2. **步长间隔 (lb_step_size)**
   - 较小值：更频繁的重排，更好的均衡
   - 较大值：较少的通信开销

3. **冗余专家数量**
   - 增加冗余专家可改善负载分布
   - 但会增加内存使用

### 监控指标

- `balancedness`: 负载均衡度 (0-1，1为完美均衡)
- `avg_tokens_per_rank`: 每个rank的平均token数
- `expert_utilization_gini`: 专家利用率基尼系数
- `rearrangement_frequency`: 重排频率

## 故障排除

### 常见问题

1. **内存不足**
   - 减少冗余专家数量
   - 调整批处理大小

2. **负载不均衡**
   - 减少 lb_step_size
   - 增加冗余专家数量

3. **通信开销过大**
   - 增加 lb_step_size
   - 关闭详细日志

### 调试选项

- 启用 `lb_log_balancedness` 查看负载统计
- 启用 `lb_collect_metrics` 收集详细指标
- 使用 Prometheus 端点监控实时状态

## 示例

参见 `examples/qwen2_moe_eplb_example.py` 获取完整的使用示例。

## 相关文档

- [EPLB 指标监控指南](eplb_metrics_guide.md)
- [EPLB 配置参考](../examples/eplb_metrics_example.py)
- [Grafana 仪表板配置](../grafana_eplb_dashboard.json)
