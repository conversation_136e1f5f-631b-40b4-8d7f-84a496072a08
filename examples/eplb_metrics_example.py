#!/usr/bin/env python3
"""
Example: EPLB Metrics Collection and Monitoring

This example demonstrates how to enable and use EPLB metrics collection
for monitoring expert parallelism load balancing quality.
"""

import time
from vllm import LLM, SamplingParams
from vllm.config import EPConfig


def main():
    """Main example function."""
    
    # Configure EPLB with metrics collection enabled
    ep_config = EPConfig(
        enable_eplb=True,
        lb_window_size=1000,
        lb_step_size=3000,
        lb_log_balancedness=True,
        lb_collect_metrics=True  # Enable detailed metrics collection
    )
    
    # Initialize LLM with EPLB configuration
    # Note: This requires a MoE model and multiple GPUs for EP
    llm = LLM(
        model="deepseek-ai/deepseek-moe-16b-base",  # Example MoE model
        tensor_parallel_size=1,
        enable_expert_parallel=True,
        ep_config=ep_config,
        # Enable Prometheus metrics endpoint
        enable_metrics=True,
        metrics_port=8080
    )
    
    # Sample prompts for testing
    prompts = [
        "The future of artificial intelligence is",
        "In a world where technology advances rapidly,",
        "The key to successful machine learning is",
        "When designing distributed systems, one must consider",
        "The impact of expert parallelism on model performance"
    ] * 10  # Repeat to generate more load
    
    sampling_params = SamplingParams(
        temperature=0.8,
        top_p=0.95,
        max_tokens=100
    )
    
    print("Starting inference with EPLB metrics collection...")
    print("Metrics will be available at http://localhost:8080/metrics")
    print("Look for metrics with prefix 'vllm_eplb_'")
    
    # Generate responses
    start_time = time.time()
    outputs = llm.generate(prompts, sampling_params)
    end_time = time.time()
    
    print(f"\nGenerated {len(outputs)} responses in {end_time - start_time:.2f} seconds")
    
    # Print some example outputs
    for i, output in enumerate(outputs[:3]):
        prompt = output.prompt
        generated_text = output.outputs[0].text
        print(f"\nPrompt {i+1}: {prompt}")
        print(f"Generated: {generated_text}")
    
    print("\nEPLB metrics have been collected and exposed.")
    print("Check the logs for EPLB balancedness information.")
    print("Access Prometheus metrics at http://localhost:8080/metrics")


if __name__ == "__main__":
    main()
