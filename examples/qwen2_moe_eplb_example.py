#!/usr/bin/env python3
"""
Example usage of Qwen2MoE with EPLB (Expert Parallelism Load Balancing).

This example demonstrates how to configure and use EPLB with Qwen2MoE models
for improved load balancing across expert parallel ranks.
"""

from vllm import LLM
from vllm.config import EPConfig

def main():
    """Main example function demonstrating Qwen2MoE with EPLB."""
    
    print("Qwen2MoE EPLB Example")
    print("=" * 50)
    
    # Configure EPLB with comprehensive settings
    ep_config = EPConfig(
        enable_eplb=True,
        lb_window_size=1000,      # Window size for expert load recording
        lb_step_size=3000,        # Interval for rearranging experts
        lb_log_balancedness=True, # Log balancedness metrics
        lb_collect_metrics=True   # Enable detailed metrics collection
    )
    
    print("EPLB Configuration:")
    print(f"  - Enable EPLB: {ep_config.enable_eplb}")
    print(f"  - Window Size: {ep_config.lb_window_size}")
    print(f"  - Step Size: {ep_config.lb_step_size}")
    print(f"  - Log Balancedness: {ep_config.lb_log_balancedness}")
    print(f"  - Collect Metrics: {ep_config.lb_collect_metrics}")
    print()
    
    # Example model configurations
    model_configs = [
        {
            "name": "Qwen2.5-MoE-A14B-Chat",
            "model": "Qwen/Qwen2.5-MoE-A14B-Chat",
            "description": "Qwen2.5 MoE model with 14B activated parameters"
        },
        {
            "name": "Qwen2-MoE-57B-A14B",
            "model": "Qwen/Qwen2-MoE-57B-A14B",
            "description": "Qwen2 MoE model with 57B total, 14B activated parameters"
        }
    ]
    
    print("Supported Qwen2MoE Models:")
    for i, config in enumerate(model_configs, 1):
        print(f"  {i}. {config['name']}")
        print(f"     Model: {config['model']}")
        print(f"     Description: {config['description']}")
    print()
    
    # Example LLM initialization (commented out as it requires actual model)
    print("Example LLM initialization:")
    print("""
    # Initialize LLM with EPLB configuration
    llm = LLM(
        model="Qwen/Qwen2.5-MoE-A14B-Chat",  # Choose your Qwen2MoE model
        tensor_parallel_size=1,               # TP size
        enable_expert_parallel=True,          # Enable expert parallelism
        ep_config=ep_config,                  # EPLB configuration
        num_redundant_experts=32,             # Additional experts for load balancing
        
        # Optional: Enable metrics endpoint
        enable_metrics=True,
        metrics_port=8080
    )
    
    # Generate text with EPLB-optimized expert routing
    prompts = [
        "Explain the concept of expert parallelism in machine learning.",
        "What are the benefits of load balancing in MoE models?",
        "How does EPLB improve inference performance?"
    ]
    
    outputs = llm.generate(prompts, sampling_params=SamplingParams(
        temperature=0.7,
        top_p=0.9,
        max_tokens=256
    ))
    
    for output in outputs:
        print(f"Prompt: {output.prompt}")
        print(f"Generated: {output.outputs[0].text}")
        print("-" * 50)
    """)
    
    print("Key EPLB Features for Qwen2MoE:")
    print("  ✓ Automatic expert load balancing")
    print("  ✓ Dynamic expert rearrangement")
    print("  ✓ Comprehensive metrics collection")
    print("  ✓ Prometheus metrics integration")
    print("  ✓ Load balancedness monitoring")
    print("  ✓ Expert utilization tracking")
    print("  ✓ Compatible with DeepSeek-V2 EPLB architecture")
    print("  ✓ Seamless integration with existing MoE infrastructure")
    print()
    
    print("Configuration Tips:")
    print("  - Set lb_window_size based on your workload pattern")
    print("  - Adjust lb_step_size to balance performance vs. overhead")
    print("  - Enable lb_collect_metrics for detailed monitoring")
    print("  - Use num_redundant_experts for better load distribution")
    print("  - Monitor balancedness metrics to tune parameters")
    print()
    
    print("Monitoring:")
    print("  - Check logs for balancedness metrics")
    print("  - Use Prometheus endpoint (if enabled) for real-time monitoring")
    print("  - Monitor expert utilization distribution")
    print("  - Track rearrangement frequency and overhead")
    print()
    
    print("For more details, see:")
    print("  - docs/eplb_metrics_guide.md")
    print("  - examples/eplb_metrics_example.py")
    print("  - Grafana dashboard: grafana_eplb_dashboard.json")

if __name__ == "__main__":
    main()
