#!/usr/bin/env python3
"""
Test script for Qwen2MoE EPLB implementation.

This script tests the basic functionality of the EPLB integration
in Qwen2MoE model without requiring actual model weights.
"""

import sys
import os

# Add the vllm directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_imports():
    """Test that all required imports work correctly."""
    print("Testing imports...")
    
    try:
        from vllm.model_executor.models.qwen2_moe import (
            Qwen2MoeForCausalLM, 
            Qwen2MoeSparseMoeBlock
        )
        from vllm.model_executor.models.interfaces import (
            MixtureOfExperts, 
            is_mixture_of_experts
        )
        print("✓ All imports successful")
        return True
    except ImportError as e:
        print(f"✗ Import failed: {e}")
        return False

def test_interface_implementation():
    """Test that Qwen2MoeForCausalLM implements MixtureOfExperts interface."""
    print("Testing interface implementation...")
    
    try:
        from vllm.model_executor.models.qwen2_moe import Qwen2MoeForCausalLM
        from vllm.model_executor.models.interfaces import MixtureOfExperts
        
        # Check if the class implements the protocol
        # Note: This is a runtime check, not a static type check
        required_attributes = [
            'expert_weights',
            'num_moe_layers',
            'num_expert_groups', 
            'num_logical_experts',
            'num_physical_experts',
            'num_local_physical_experts',
            'num_routed_experts',
            'num_redundant_experts',
            'set_eplb_state'
        ]
        
        for attr in required_attributes:
            if not hasattr(Qwen2MoeForCausalLM, attr):
                print(f"✗ Missing attribute: {attr}")
                return False
        
        print("✓ All required attributes present")
        return True
        
    except Exception as e:
        print(f"✗ Interface test failed: {e}")
        return False

def test_moe_block_methods():
    """Test that Qwen2MoeSparseMoeBlock has required EPLB methods."""
    print("Testing MoE block methods...")
    
    try:
        from vllm.model_executor.models.qwen2_moe import Qwen2MoeSparseMoeBlock
        
        required_methods = [
            'get_expert_weights',
            'set_eplb_state'
        ]
        
        for method in required_methods:
            if not hasattr(Qwen2MoeSparseMoeBlock, method):
                print(f"✗ Missing method: {method}")
                return False
        
        print("✓ All required methods present")
        return True
        
    except Exception as e:
        print(f"✗ MoE block test failed: {e}")
        return False

def test_syntax():
    """Test that the Python syntax is correct."""
    print("Testing syntax...")
    
    try:
        import py_compile
        py_compile.compile('vllm/model_executor/models/qwen2_moe.py', doraise=True)
        print("✓ Syntax check passed")
        return True
    except py_compile.PyCompileError as e:
        print(f"✗ Syntax error: {e}")
        return False

def main():
    """Run all tests."""
    print("Running Qwen2MoE EPLB tests...\n")
    
    tests = [
        test_syntax,
        test_imports,
        test_interface_implementation,
        test_moe_block_methods,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! EPLB implementation looks good.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
