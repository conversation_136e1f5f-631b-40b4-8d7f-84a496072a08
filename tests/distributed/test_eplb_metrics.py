# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project

import pytest
import torch
from unittest.mock import MagicMock, patch

from vllm.distributed.eplb.metrics import EPLBMetrics, EPLBMetricsCollector


class MockEplbState:
    """Mock EPLB state for testing."""
    
    def __init__(self):
        self.expert_load_pass = torch.tensor([[10, 20, 30], [15, 25, 35]])
        self.logical_replica_count = torch.tensor([[2, 3, 1], [1, 2, 3]])
        self.expert_rearrangement_step = 5


def test_eplb_metrics_basic():
    """Test basic EPLB metrics calculation."""
    metrics = EPLBMetrics()
    
    # Test default values
    assert metrics.balancedness == 0.0
    assert metrics.avg_tokens_per_rank == 0.0
    assert metrics.expert_utilization_gini == 0.0


def test_eplb_metrics_collector_init():
    """Test EPLB metrics collector initialization."""
    collector = EPLBMetricsCollector(enable_prometheus=False)
    
    assert collector.enable_prometheus is False
    assert collector._total_steps == 0
    assert collector._total_rearrangements == 0


def test_gini_coefficient_calculation():
    """Test Gini coefficient calculation."""
    collector = EPLBMetricsCollector(enable_prometheus=False)
    
    # Perfect equality
    equal_values = torch.tensor([10.0, 10.0, 10.0, 10.0])
    gini_equal = collector.calculate_gini_coefficient(equal_values)
    assert abs(gini_equal) < 0.01  # Should be close to 0
    
    # Maximum inequality
    unequal_values = torch.tensor([100.0, 0.0, 0.0, 0.0])
    gini_unequal = collector.calculate_gini_coefficient(unequal_values)
    assert gini_unequal > 0.7  # Should be high
    
    # Empty tensor
    empty_values = torch.tensor([])
    gini_empty = collector.calculate_gini_coefficient(empty_values)
    assert gini_empty == 0.0


def test_metrics_collection():
    """Test metrics collection from EPLB state."""
    collector = EPLBMetricsCollector(enable_prometheus=False)
    mock_state = MockEplbState()
    
    metrics = collector.collect_metrics(mock_state, "test_model", 0)
    
    # Check that metrics are calculated
    assert metrics.avg_tokens_per_rank > 0
    assert metrics.max_tokens_per_rank > 0
    assert metrics.balancedness > 0
    assert metrics.steps_since_rearrangement == 5
    assert metrics.avg_expert_replicas > 0


def test_rearrangement_recording():
    """Test rearrangement event recording."""
    collector = EPLBMetricsCollector(enable_prometheus=False)
    
    # Record some rearrangements
    collector.record_rearrangement(0.5, 1024)
    collector.record_rearrangement(0.8, 2048)
    
    assert collector._total_rearrangements == 2
    assert len(collector._rearrangement_times) == 2
    assert collector._rearrangement_times[-1] == 0.8


def test_metrics_reporting_without_prometheus():
    """Test metrics reporting without Prometheus."""
    collector = EPLBMetricsCollector(enable_prometheus=False)
    mock_state = MockEplbState()
    
    metrics = collector.collect_metrics(mock_state, "test_model", 0)
    
    # Should not raise any exceptions
    collector.report_metrics(metrics, "test_model", 0)


@pytest.mark.skipif(not hasattr(torch, 'cuda') or not torch.cuda.is_available(),
                    reason="CUDA not available")
def test_metrics_with_cuda_tensors():
    """Test metrics calculation with CUDA tensors."""
    collector = EPLBMetricsCollector(enable_prometheus=False)
    
    # Create mock state with CUDA tensors
    mock_state = MockEplbState()
    mock_state.expert_load_pass = mock_state.expert_load_pass.cuda()
    mock_state.logical_replica_count = mock_state.logical_replica_count.cuda()
    
    metrics = collector.collect_metrics(mock_state, "test_model", 0)
    
    # Should work with CUDA tensors
    assert metrics.avg_tokens_per_rank > 0
    assert metrics.balancedness > 0


def test_metrics_with_zero_load():
    """Test metrics calculation with zero load."""
    collector = EPLBMetricsCollector(enable_prometheus=False)
    
    # Create mock state with zero load
    mock_state = MockEplbState()
    mock_state.expert_load_pass = torch.zeros((2, 3))
    
    metrics = collector.collect_metrics(mock_state, "test_model", 0)
    
    # Should handle zero load gracefully
    assert metrics.avg_tokens_per_rank == 0.0
    assert metrics.max_tokens_per_rank == 0.0
    assert metrics.balancedness == 0.0


@patch('vllm.distributed.eplb.metrics.prometheus_client')
def test_prometheus_metrics_setup(mock_prometheus):
    """Test Prometheus metrics setup."""
    mock_prometheus.Gauge = MagicMock()
    mock_prometheus.Counter = MagicMock()
    mock_prometheus.Histogram = MagicMock()
    
    collector = EPLBMetricsCollector(enable_prometheus=True)
    
    # Should create Prometheus metrics
    assert mock_prometheus.Gauge.call_count > 0
    assert mock_prometheus.Counter.call_count > 0
    assert mock_prometheus.Histogram.call_count > 0


def test_metrics_frequency_calculation():
    """Test rearrangement frequency calculation."""
    collector = EPLBMetricsCollector(enable_prometheus=False)
    mock_state = MockEplbState()
    
    # Simulate some steps and rearrangements
    for i in range(10):
        collector.collect_metrics(mock_state, "test_model", 0)
        if i % 3 == 0:  # Rearrange every 3 steps
            collector.record_rearrangement(0.5)
    
    metrics = collector.collect_metrics(mock_state, "test_model", 0)
    
    # Check frequency calculation
    expected_frequency = collector._total_rearrangements / collector._total_steps
    assert abs(metrics.rearrangement_frequency - expected_frequency) < 0.01


def test_large_rearrangement_history():
    """Test handling of large rearrangement history."""
    collector = EPLBMetricsCollector(enable_prometheus=False)
    
    # Record many rearrangements
    for i in range(150):
        collector.record_rearrangement(0.1 * i)
    
    # Should limit history size
    assert len(collector._rearrangement_times) <= 100


if __name__ == "__main__":
    pytest.main([__file__])
