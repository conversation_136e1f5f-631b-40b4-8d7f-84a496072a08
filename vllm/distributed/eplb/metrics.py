# SPDX-License-Identifier: Apache-2.0
# SPDX-FileCopyrightText: Copyright contributors to the vLLM project
"""
EPLB (Expert Parallelism Load Balancing) metrics collection and reporting.

This module provides comprehensive metrics for monitoring EPLB performance,
load balancing quality, and system efficiency.
"""

import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Union

import torch

from vllm.logger import init_logger

logger = init_logger(__name__)

try:
    import prometheus_client
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    logger.warning("Prometheus client not available. EPLB metrics will be logged only.")


@dataclass
class EPLBMetrics:
    """EPLB metrics data structure."""
    
    # Core load balancing metrics
    balancedness: float = 0.0
    """Load balancedness ratio (avg_load / max_load). Range: [0, 1], 1 = perfect balance."""
    
    avg_tokens_per_rank: float = 0.0
    """Average number of tokens processed per rank."""
    
    max_tokens_per_rank: int = 0
    """Maximum number of tokens processed by any rank."""
    
    min_tokens_per_rank: int = 0
    """Minimum number of tokens processed by any rank."""
    
    load_variance: float = 0.0
    """Variance in load distribution across ranks."""
    
    load_std: float = 0.0
    """Standard deviation in load distribution across ranks."""
    
    # Expert utilization metrics
    avg_expert_replicas: float = 0.0
    """Average number of replicas per logical expert."""
    
    max_expert_replicas: int = 0
    """Maximum number of replicas for any logical expert."""
    
    min_expert_replicas: int = 0
    """Minimum number of replicas for any logical expert."""
    
    expert_utilization_gini: float = 0.0
    """Gini coefficient for expert utilization (0 = equal, 1 = maximum inequality)."""
    
    # Rearrangement metrics
    rearrangement_frequency: float = 0.0
    """Frequency of expert rearrangements (rearrangements per step)."""
    
    last_rearrangement_time: float = 0.0
    """Time taken for the last expert rearrangement (seconds)."""
    
    steps_since_rearrangement: int = 0
    """Number of steps since the last rearrangement."""
    
    # Communication metrics
    communication_volume: int = 0
    """Volume of data communicated during rearrangement (bytes)."""
    
    # Memory metrics
    expert_memory_usage: float = 0.0
    """Memory usage for expert weights (MB)."""
    
    # Performance impact metrics
    eplb_overhead_ratio: float = 0.0
    """EPLB overhead as a ratio of total inference time."""


class EPLBMetricsCollector:
    """Collects and reports EPLB metrics."""
    
    def __init__(self, enable_prometheus: bool = True):
        self.enable_prometheus = enable_prometheus and PROMETHEUS_AVAILABLE
        self._setup_prometheus_metrics()
        
        # Internal state
        self._total_steps = 0
        self._total_rearrangements = 0
        self._rearrangement_times: List[float] = []
        
    def _setup_prometheus_metrics(self):
        """Setup Prometheus metrics if available."""
        if not self.enable_prometheus:
            return
            
        # Core load balancing metrics
        self.gauge_balancedness = prometheus_client.Gauge(
            'vllm_eplb_balancedness',
            'EPLB load balancedness ratio (avg_load / max_load)',
            ['model_name', 'ep_rank']
        )
        
        self.gauge_avg_tokens_per_rank = prometheus_client.Gauge(
            'vllm_eplb_avg_tokens_per_rank',
            'Average tokens processed per rank',
            ['model_name', 'ep_rank']
        )
        
        self.gauge_max_tokens_per_rank = prometheus_client.Gauge(
            'vllm_eplb_max_tokens_per_rank',
            'Maximum tokens processed by any rank',
            ['model_name', 'ep_rank']
        )
        
        self.gauge_load_variance = prometheus_client.Gauge(
            'vllm_eplb_load_variance',
            'Variance in load distribution across ranks',
            ['model_name', 'ep_rank']
        )
        
        # Expert utilization metrics
        self.gauge_avg_expert_replicas = prometheus_client.Gauge(
            'vllm_eplb_avg_expert_replicas',
            'Average number of replicas per logical expert',
            ['model_name', 'ep_rank']
        )
        
        self.gauge_expert_utilization_gini = prometheus_client.Gauge(
            'vllm_eplb_expert_utilization_gini',
            'Gini coefficient for expert utilization',
            ['model_name', 'ep_rank']
        )
        
        # Rearrangement metrics
        self.counter_rearrangements = prometheus_client.Counter(
            'vllm_eplb_rearrangements_total',
            'Total number of expert rearrangements',
            ['model_name', 'ep_rank']
        )
        
        self.histogram_rearrangement_time = prometheus_client.Histogram(
            'vllm_eplb_rearrangement_time_seconds',
            'Time taken for expert rearrangement',
            ['model_name', 'ep_rank'],
            buckets=[0.01, 0.05, 0.1, 0.2, 0.5, 1.0, 2.0, 5.0]
        )
        
        self.gauge_steps_since_rearrangement = prometheus_client.Gauge(
            'vllm_eplb_steps_since_rearrangement',
            'Steps since last expert rearrangement',
            ['model_name', 'ep_rank']
        )
        
        # Performance metrics
        self.gauge_eplb_overhead_ratio = prometheus_client.Gauge(
            'vllm_eplb_overhead_ratio',
            'EPLB overhead as ratio of total inference time',
            ['model_name', 'ep_rank']
        )
        
        self.gauge_expert_memory_usage = prometheus_client.Gauge(
            'vllm_eplb_expert_memory_usage_mb',
            'Memory usage for expert weights in MB',
            ['model_name', 'ep_rank']
        )
        
        # Communication metrics
        self.counter_communication_volume = prometheus_client.Counter(
            'vllm_eplb_communication_volume_bytes_total',
            'Total communication volume during rearrangements',
            ['model_name', 'ep_rank']
        )
    
    def calculate_gini_coefficient(self, values: torch.Tensor) -> float:
        """Calculate Gini coefficient for measuring inequality."""
        if len(values) == 0:
            return 0.0
            
        sorted_values = torch.sort(values.float())[0]
        n = len(sorted_values)
        
        if torch.sum(sorted_values) == 0:
            return 0.0
            
        cumsum = torch.cumsum(sorted_values, dim=0)
        gini = (2 * torch.sum((torch.arange(1, n+1, device=values.device).float() * sorted_values))) / (n * torch.sum(sorted_values)) - (n + 1) / n
        return gini.item()
    
    def collect_metrics(self, eplb_state, model_name: str = "unknown", ep_rank: int = 0) -> EPLBMetrics:
        """Collect comprehensive EPLB metrics."""
        from .eplb_state import EplbState
        
        metrics = EPLBMetrics()
        
        # Calculate load distribution metrics
        load_per_rank = eplb_state.expert_load_pass.sum(dim=-1).float()
        
        if len(load_per_rank) > 0 and torch.sum(load_per_rank) > 0:
            metrics.avg_tokens_per_rank = torch.mean(load_per_rank).item()
            metrics.max_tokens_per_rank = torch.max(load_per_rank).item()
            metrics.min_tokens_per_rank = torch.min(load_per_rank).item()
            metrics.load_variance = torch.var(load_per_rank).item()
            metrics.load_std = torch.std(load_per_rank).item()
            
            # Calculate balancedness
            if metrics.max_tokens_per_rank > 0:
                metrics.balancedness = metrics.avg_tokens_per_rank / metrics.max_tokens_per_rank
        
        # Expert utilization metrics
        replica_counts = eplb_state.logical_replica_count.float()
        if len(replica_counts) > 0:
            metrics.avg_expert_replicas = torch.mean(replica_counts).item()
            metrics.max_expert_replicas = torch.max(replica_counts).item()
            metrics.min_expert_replicas = torch.min(replica_counts).item()
            metrics.expert_utilization_gini = self.calculate_gini_coefficient(replica_counts)
        
        # Rearrangement metrics
        metrics.steps_since_rearrangement = eplb_state.expert_rearrangement_step
        self._total_steps += 1
        
        if len(self._rearrangement_times) > 0:
            metrics.last_rearrangement_time = self._rearrangement_times[-1]
            metrics.rearrangement_frequency = self._total_rearrangements / self._total_steps
        
        return metrics
    
    def record_rearrangement(self, duration: float, communication_volume: int = 0):
        """Record a rearrangement event."""
        self._total_rearrangements += 1
        self._rearrangement_times.append(duration)
        
        # Keep only recent rearrangement times
        if len(self._rearrangement_times) > 100:
            self._rearrangement_times = self._rearrangement_times[-50:]
    
    def report_metrics(self, metrics: EPLBMetrics, model_name: str = "unknown", ep_rank: int = 0):
        """Report metrics to configured backends."""
        
        # Log metrics
        logger.info(
            f"EPLB Metrics [rank={ep_rank}]: "
            f"balancedness={metrics.balancedness:.4f}, "
            f"avg_tokens={metrics.avg_tokens_per_rank:.2f}, "
            f"max_tokens={metrics.max_tokens_per_rank}, "
            f"load_std={metrics.load_std:.2f}, "
            f"expert_gini={metrics.expert_utilization_gini:.4f}, "
            f"steps_since_rearrange={metrics.steps_since_rearrangement}"
        )
        
        # Report to Prometheus if enabled
        if self.enable_prometheus:
            labels = {'model_name': model_name, 'ep_rank': str(ep_rank)}
            
            self.gauge_balancedness.labels(**labels).set(metrics.balancedness)
            self.gauge_avg_tokens_per_rank.labels(**labels).set(metrics.avg_tokens_per_rank)
            self.gauge_max_tokens_per_rank.labels(**labels).set(metrics.max_tokens_per_rank)
            self.gauge_load_variance.labels(**labels).set(metrics.load_variance)
            self.gauge_avg_expert_replicas.labels(**labels).set(metrics.avg_expert_replicas)
            self.gauge_expert_utilization_gini.labels(**labels).set(metrics.expert_utilization_gini)
            self.gauge_steps_since_rearrangement.labels(**labels).set(metrics.steps_since_rearrangement)
            
            if metrics.last_rearrangement_time > 0:
                self.histogram_rearrangement_time.labels(**labels).observe(metrics.last_rearrangement_time)
            
            if metrics.eplb_overhead_ratio > 0:
                self.gauge_eplb_overhead_ratio.labels(**labels).set(metrics.eplb_overhead_ratio)


# Global metrics collector instance
_metrics_collector: Optional[EPLBMetricsCollector] = None

def get_eplb_metrics_collector() -> EPLBMetricsCollector:
    """Get the global EPLB metrics collector."""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = EPLBMetricsCollector()
    return _metrics_collector
